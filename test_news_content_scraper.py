#!/usr/bin/env python3
"""
测试从PANews URL爬取完整新闻内容的脚本
验证能否获取比API更完整的content
"""

import requests
import re
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import time
import json


def scrape_panews_content(url: str, use_proxy: bool = False) -> dict:
    """
    从PANews URL爬取完整新闻内容
    
    Args:
        url: PANews新闻URL
        use_proxy: 是否使用代理
        
    Returns:
        dict: 包含title, content, publish_time等信息
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-C<PERSON>,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    proxies = None
    if use_proxy:
        # 这里可以配置代理
        proxies = {
            'http': 'http://127.0.0.1:7890',
            'https': 'http://127.0.0.1:7890'
        }
    
    try:
        print(f"正在爬取: {url}")
        response = requests.get(
            url,
            headers=headers,
            proxies=proxies,
            timeout=30,
            verify=False
        )
        
        if response.status_code != 200:
            print(f"请求失败: HTTP {response.status_code}")
            return {}
        
        # 解析HTML
        soup = BeautifulSoup(response.content, 'html.parser')
        
        result = {
            'url': url,
            'title': '',
            'content': '',
            'publish_time': '',
            'author': '',
            'tags': []
        }
        
        # 提取标题
        title_selectors = [
            'h1.article-title',
            'h1.news-title', 
            '.article-header h1',
            '.news-header h1',
            'h1',
            '.title'
        ]
        
        for selector in title_selectors:
            title_elem = soup.select_one(selector)
            if title_elem:
                result['title'] = title_elem.get_text().strip()
                break
        
        # 提取内容
        content_selectors = [
            '.article-content',
            '.news-content',
            '.content',
            '.article-body',
            '.news-body'
        ]
        
        content_text = ""
        for selector in content_selectors:
            content_elem = soup.select_one(selector)
            if content_elem:
                # 移除脚本和样式标签
                for script in content_elem(["script", "style"]):
                    script.decompose()
                content_text = content_elem.get_text().strip()
                break
        
        # 如果没找到专门的内容区域，尝试从整个页面提取
        if not content_text:
            # 移除导航、侧边栏等无关内容
            for elem in soup(["nav", "header", "footer", "aside", "script", "style"]):
                elem.decompose()
            content_text = soup.get_text()
        
        # 清理内容
        content_text = re.sub(r'\s+', ' ', content_text).strip()
        result['content'] = content_text
        
        # 提取发布时间
        time_selectors = [
            '.publish-time',
            '.news-time',
            '.article-time',
            'time',
            '.date'
        ]
        
        for selector in time_selectors:
            time_elem = soup.select_one(selector)
            if time_elem:
                result['publish_time'] = time_elem.get_text().strip()
                break
        
        # 提取作者
        author_selectors = [
            '.author',
            '.news-author',
            '.article-author'
        ]
        
        for selector in author_selectors:
            author_elem = soup.select_one(selector)
            if author_elem:
                result['author'] = author_elem.get_text().strip()
                break
        
        # 提取标签
        tag_selectors = [
            '.tags a',
            '.article-tags a',
            '.news-tags a'
        ]
        
        for selector in tag_selectors:
            tag_elems = soup.select(selector)
            if tag_elems:
                result['tags'] = [tag.get_text().strip() for tag in tag_elems]
                break
        
        print(f"✅ 爬取成功: {result['title'][:50]}...")
        print(f"内容长度: {len(result['content'])} 字符")
        
        return result
        
    except Exception as e:
        print(f"❌ 爬取失败: {e}")
        return {}


def test_panews_scraping():
    """测试PANews内容爬取"""
    print("=" * 60)
    print("测试PANews内容爬取功能")
    print("=" * 60)
    
    # 测试URL列表（这些是示例URL，实际使用时需要从API获取）
    test_urls = [
        "https://www.panewslab.com/zh/newsflash/123456",  # 快讯类型
        "https://www.panewslab.com/zh/articledetails/123456.html",  # 文章类型
    ]
    
    # 首先测试API获取新闻列表
    print("\n1. 测试API获取新闻列表...")
    api_url = "https://www.panewslab.com/webapi/flashnews?LId=1&Rn=3&tw=0"
    
    try:
        response = requests.get(
            api_url,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Referer': 'https://www.panewslab.com',
            },
            timeout=30,
            verify=False
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API请求成功")
            
            # 解析新闻列表
            if 'data' in data and 'flashNews' in data['data']:
                flash_news = data['data']['flashNews']
                if flash_news and len(flash_news) > 0 and 'list' in flash_news[0]:
                    news_list = flash_news[0]['list']
                    print(f"获取到 {len(news_list)} 条新闻")
                    
                    # 测试爬取前几条新闻的完整内容
                    for i, news_item in enumerate(news_list[:2]):  # 只测试前2条
                        print(f"\n2.{i+1} 测试爬取新闻内容...")
                        
                        # 构建URL
                        news_id = news_item.get('id', '')
                        item_type = news_item.get('type', 0)
                        
                        if news_id:
                            if item_type == 2:  # 快讯类型
                                url = f"https://www.panewslab.com/zh/newsflash/{news_id}"
                            else:
                                url = f"https://www.panewslab.com/zh/articledetails/{news_id}.html"
                            
                            print(f"API标题: {news_item.get('title', '')[:50]}...")
                            print(f"API内容: {news_item.get('desc', '')[:100]}...")
                            print(f"URL: {url}")
                            
                            # 爬取完整内容
                            scraped_content = scrape_panews_content(url)
                            
                            if scraped_content:
                                print(f"爬取标题: {scraped_content['title'][:50]}...")
                                print(f"爬取内容: {scraped_content['content'][:200]}...")
                                
                                # 比较内容长度
                                api_content_len = len(news_item.get('desc', ''))
                                scraped_content_len = len(scraped_content['content'])
                                print(f"内容长度对比: API={api_content_len}, 爬取={scraped_content_len}")
                                
                                if scraped_content_len > api_content_len:
                                    print("✅ 爬取的内容比API更完整")
                                else:
                                    print("⚠️ 爬取的内容可能不比API更完整")
                            
                            time.sleep(2)  # 避免请求过快
                        
                else:
                    print("❌ 无效的新闻数据结构")
            else:
                print("❌ 无效的API响应结构")
        else:
            print(f"❌ API请求失败: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")


def test_content_comparison():
    """测试内容对比功能"""
    print("\n" + "=" * 60)
    print("测试内容对比功能")
    print("=" * 60)
    
    # 模拟两次API调用的结果
    previous_news = [
        {"id": "123", "title": "旧新闻1"},
        {"id": "124", "title": "旧新闻2"},
    ]
    
    current_news = [
        {"id": "123", "title": "旧新闻1"},  # 重复
        {"id": "124", "title": "旧新闻2"},  # 重复
        {"id": "125", "title": "新新闻1"},  # 新增
        {"id": "126", "title": "新新闻2"},  # 新增
    ]
    
    # 找出新增的新闻
    previous_ids = {item["id"] for item in previous_news}
    new_news = [item for item in current_news if item["id"] not in previous_ids]
    
    print(f"之前的新闻: {len(previous_news)} 条")
    print(f"当前的新闻: {len(current_news)} 条")
    print(f"新增的新闻: {len(new_news)} 条")
    
    for news in new_news:
        print(f"  - {news['title']}")
    
    print("✅ 内容对比功能正常")


if __name__ == "__main__":
    test_panews_scraping()
    test_content_comparison()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
