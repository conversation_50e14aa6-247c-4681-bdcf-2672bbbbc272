#!/usr/bin/env python3
"""
测试优化后的PANews数据客户端
验证交易对提取和内容爬取功能
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加当前目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nautilus_trader.model.identifiers import ClientId, Venue, TraderId
from nautilus_trader.common.component import MessageBus, TestClock
from nautilus_trader.cache.cache import Cache
from nautilus_trader.test_kit.providers import TestInstrumentProvider

from strategy.panews_data_client import PANewsDataClient, PANewsDataClientConfig
from strategy.news_data_type import NewsData


class MockInstrument:
    """模拟 Instrument 对象"""
    def __init__(self, instrument_id_str: str):
        self.id = MockInstrumentId(instrument_id_str)


class MockInstrumentId:
    """模拟 InstrumentId 对象"""
    def __init__(self, value: str):
        self.value = value


class MockCache:
    """模拟 Cache 对象"""
    def __init__(self, instrument_ids: list):
        self._instruments = [MockInstrument(id_str) for id_str in instrument_ids]
    
    def instruments(self, venue=None):
        """返回模拟的 instruments 列表"""
        return self._instruments


async def test_optimized_panews_client():
    """测试优化后的PANews客户端"""
    print("=" * 60)
    print("测试优化后的PANews数据客户端")
    print("=" * 60)
    
    # 创建真正的 cache 并添加测试 instruments
    cache = Cache()

    # 添加一些测试用的加密货币永续合约
    btc_perp = TestInstrumentProvider.btcusdt_perp_binance()
    eth_perp = TestInstrumentProvider.ethusdt_perp_binance()

    cache.add_instrument(btc_perp)
    cache.add_instrument(eth_perp)

    print(f"✅ 添加了 {len(cache.instruments())} 个测试 instruments")
    
    # 创建必要的组件
    clock = TestClock()
    trader_id = TraderId("TESTER-001")
    msgbus = MessageBus(trader_id=trader_id, clock=clock)
    
    # 创建配置
    config = PANewsDataClientConfig(
        scraping_interval=60,  # 1分钟测试间隔
        max_news_per_request=5,
        enable_proxy=False,  # 禁用代理以简化测试
        enable_content_scraping=True,
        enable_instrument_extraction=True,
        min_confidence_threshold=0.1,  # 降低阈值以便测试
        request_timeout=30
    )
    
    # 创建客户端
    client = PANewsDataClient(
        loop=asyncio.get_event_loop(),
        client_id=ClientId("PANewsTest"),
        venue=Venue("PANEWS"),
        msgbus=msgbus,
        cache=cache,
        clock=clock,
        config=config,
        name="PANewsTestClient"
    )
    
    # 设置数据处理器
    received_news = []
    
    def handle_news_data(data):
        """处理接收到的新闻数据"""
        if hasattr(data, 'data') and isinstance(data.data, NewsData):
            news = data.data
            received_news.append(news)
            print(f"📰 接收到新闻: {news.instrument_id.value}")
            print(f"   标题: {news.title[:50]}...")
            print(f"   置信度: {news.confidence:.3f}")
            print(f"   内容长度: {len(news.content)} 字符")
            print()
    
    # 注册数据处理器
    msgbus.register(endpoint="DataEngine.process", handler=handle_news_data)
    
    try:
        print("1. 连接客户端...")
        await client._connect()
        print("✅ 客户端连接成功")
        
        print("\n2. 手动触发一次新闻爬取...")
        # 手动触发一次爬取来测试功能
        await client._scrape_and_publish_news()
        
        print(f"\n3. 等待数据处理...")
        await asyncio.sleep(2)  # 等待数据处理
        
        print(f"\n4. 测试结果:")
        print(f"   接收到 {len(received_news)} 条新闻数据")
        
        if received_news:
            print("\n   新闻详情:")
            for i, news in enumerate(received_news[:3], 1):  # 只显示前3条
                print(f"   {i}. {news.instrument_id.value}")
                print(f"      标题: {news.title}")
                print(f"      置信度: {news.confidence:.3f}")
                print(f"      来源: {news.source}")
                print(f"      URL: {news.url}")
                print()
        
        # 测试组件状态
        print("5. 检查组件状态:")
        if client._instrument_extractor:
            print("   ✅ 交易对提取器已初始化")
            print(f"   - 基础符号映射数量: {len(client._instrument_extractor.base_symbol_to_instrument_id)}")
        else:
            print("   ❌ 交易对提取器未初始化")
        
        if client._content_scraper:
            print("   ✅ 内容爬取器已初始化")
        else:
            print("   ❌ 内容爬取器未初始化")
        
        # 测试代理状态
        proxy_status = await client.get_proxy_status()
        print(f"   代理状态: {proxy_status.get('proxy_enabled', False)}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print("\n6. 断开连接...")
        await client._disconnect()
        print("✅ 客户端断开连接")


async def test_news_processing_components():
    """单独测试新闻处理组件"""
    print("\n" + "=" * 60)
    print("测试新闻处理组件")
    print("=" * 60)
    
    # 测试交易对提取器
    print("1. 测试交易对提取器...")
    
    from strategy.news_instrument_extractor_v2 import NewsInstrumentExtractor
    from nautilus_trader.model.identifiers import Venue
    
    # 创建模拟 cache
    mock_instrument_ids = [
        "BTCUSDT-PERP.BINANCE",
        "ETHUSDT-PERP.BINANCE",
        "1000PEPEUSDT-PERP.BINANCE",
    ]
    mock_cache = MockCache(mock_instrument_ids)
    
    extractor = NewsInstrumentExtractor(
        cache=mock_cache,
        venue=Venue("BINANCE"),
        max_symbols=3
    )
    
    # 测试用例
    test_cases = [
        {
            'title': 'BTC价格突破50000美元',
            'content': '比特币今日价格突破50000美元大关'
        },
        {
            'title': 'PEPE meme币热度不减',
            'content': 'PEPE代币继续受到关注，交易活跃'
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {case['title']}")
        
        instrument_ids = extractor.extract_instrument_ids(
            case['title'].lower(),
            case['content'].lower()
        )
        
        print(f"提取到的交易对: {[id.value for id in instrument_ids]}")
    
    # 测试内容爬取器
    print("\n2. 测试内容爬取器...")
    
    from strategy.news_content_scraper import NewsContentScraper
    
    scraper = NewsContentScraper(timeout=10, use_proxy=False)
    
    # 测试一个简单的URL（如果可用）
    test_url = "https://httpbin.org/html"  # 测试URL
    
    try:
        result = scraper.scrape_content(test_url)
        if result:
            print(f"✅ 内容爬取成功")
            print(f"   标题: {result.get('title', 'N/A')}")
            print(f"   内容长度: {len(result.get('content', ''))}")
        else:
            print("⚠️ 内容爬取返回空结果")
    except Exception as e:
        print(f"❌ 内容爬取失败: {e}")


async def main():
    """主测试函数"""
    await test_optimized_panews_client()
    await test_news_processing_components()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
