"""
新闻内容爬取器
从PANews URL爬取完整的新闻内容
"""

import requests
import re
from bs4 import BeautifulSoup
from typing import Dict, Optional
import logging
from urllib.parse import urljoin


class NewsContentScraper:
    """新闻内容爬取器"""
    
    def __init__(self, timeout: int = 30, use_proxy: bool = False, proxy_url: Optional[str] = None):
        self.timeout = timeout
        self.use_proxy = use_proxy
        self.proxy_url = proxy_url
        
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        self.logger = logging.getLogger(__name__)
    
    def scrape_content(self, url: str) -> Dict[str, str]:
        """
        从URL爬取完整新闻内容
        
        Args:
            url: 新闻URL
            
        Returns:
            dict: 包含title, content, publish_time等信息
        """
        try:
            proxies = None
            if self.use_proxy and self.proxy_url:
                proxies = {
                    'http': self.proxy_url,
                    'https': self.proxy_url
                }
            
            self.logger.debug(f"正在爬取: {url}")
            
            response = requests.get(
                url,
                headers=self.headers,
                proxies=proxies,
                timeout=self.timeout,
                verify=False
            )
            
            if response.status_code != 200:
                self.logger.warning(f"请求失败: HTTP {response.status_code} for {url}")
                return {}
            
            # 解析HTML
            soup = BeautifulSoup(response.content, 'html.parser')
            
            result = {
                'url': url,
                'title': '',
                'content': '',
                'publish_time': '',
                'author': '',
                'tags': []
            }
            
            # 提取标题
            result['title'] = self._extract_title(soup)
            
            # 提取内容
            result['content'] = self._extract_content(soup)
            
            # 提取发布时间
            result['publish_time'] = self._extract_publish_time(soup)
            
            # 提取作者
            result['author'] = self._extract_author(soup)
            
            # 提取标签
            result['tags'] = self._extract_tags(soup)
            
            self.logger.debug(f"✅ 爬取成功: {result['title'][:50]}...")
            self.logger.debug(f"内容长度: {len(result['content'])} 字符")
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 爬取失败 {url}: {e}")
            return {}
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """提取标题"""
        title_selectors = [
            'h1.article-title',
            'h1.news-title', 
            '.article-header h1',
            '.news-header h1',
            'h1',
            '.title',
            '.article-title',
            '.news-title'
        ]
        
        for selector in title_selectors:
            title_elem = soup.select_one(selector)
            if title_elem:
                title = title_elem.get_text().strip()
                if title and len(title) > 5:  # 确保标题有意义
                    return title
        
        # 如果都没找到，尝试从页面标题提取
        title_tag = soup.find('title')
        if title_tag:
            title = title_tag.get_text().strip()
            # 移除网站名称
            title = re.sub(r'\s*-\s*PANews.*$', '', title)
            title = re.sub(r'\s*\|\s*PANews.*$', '', title)
            return title
        
        return ""
    
    def _extract_content(self, soup: BeautifulSoup) -> str:
        """提取内容"""
        content_selectors = [
            '.article-content',
            '.news-content',
            '.content',
            '.article-body',
            '.news-body',
            '.post-content',
            '.entry-content',
            '.main-content'
        ]
        
        content_text = ""
        for selector in content_selectors:
            content_elem = soup.select_one(selector)
            if content_elem:
                # 移除脚本和样式标签
                for script in content_elem(["script", "style", "nav", "header", "footer", "aside"]):
                    script.decompose()
                content_text = content_elem.get_text().strip()
                if content_text and len(content_text) > 50:  # 确保内容有意义
                    break
        
        # 如果没找到专门的内容区域，尝试从整个页面提取
        if not content_text or len(content_text) < 50:
            # 移除导航、侧边栏等无关内容
            for elem in soup(["nav", "header", "footer", "aside", "script", "style", "form", "input"]):
                elem.decompose()
            
            # 尝试找到主要内容区域
            main_selectors = ['main', '.main', '#main', '.container', '.wrapper']
            for selector in main_selectors:
                main_elem = soup.select_one(selector)
                if main_elem:
                    content_text = main_elem.get_text().strip()
                    break
            
            if not content_text:
                content_text = soup.get_text()
        
        # 清理内容
        content_text = re.sub(r'\s+', ' ', content_text).strip()
        
        # 移除常见的无关内容
        content_text = re.sub(r'分享到.*?$', '', content_text)
        content_text = re.sub(r'相关阅读.*?$', '', content_text)
        content_text = re.sub(r'免责声明.*?$', '', content_text)
        
        return content_text
    
    def _extract_publish_time(self, soup: BeautifulSoup) -> str:
        """提取发布时间"""
        time_selectors = [
            '.publish-time',
            '.news-time',
            '.article-time',
            'time',
            '.date',
            '.post-date',
            '.entry-date'
        ]
        
        for selector in time_selectors:
            time_elem = soup.select_one(selector)
            if time_elem:
                time_text = time_elem.get_text().strip()
                if time_text:
                    return time_text
                
                # 检查datetime属性
                datetime_attr = time_elem.get('datetime')
                if datetime_attr:
                    return datetime_attr
        
        return ""
    
    def _extract_author(self, soup: BeautifulSoup) -> str:
        """提取作者"""
        author_selectors = [
            '.author',
            '.news-author',
            '.article-author',
            '.post-author',
            '.byline'
        ]
        
        for selector in author_selectors:
            author_elem = soup.select_one(selector)
            if author_elem:
                author = author_elem.get_text().strip()
                if author:
                    return author
        
        return ""
    
    def _extract_tags(self, soup: BeautifulSoup) -> list:
        """提取标签"""
        tag_selectors = [
            '.tags a',
            '.article-tags a',
            '.news-tags a',
            '.post-tags a',
            '.tag-list a'
        ]
        
        for selector in tag_selectors:
            tag_elems = soup.select(selector)
            if tag_elems:
                tags = [tag.get_text().strip() for tag in tag_elems]
                return [tag for tag in tags if tag]
        
        return []
    
    def is_content_better(self, scraped_content: str, api_content: str) -> bool:
        """
        判断爬取的内容是否比API内容更好
        
        Args:
            scraped_content: 爬取的内容
            api_content: API返回的内容
            
        Returns:
            bool: 如果爬取的内容更好则返回True
        """
        if not scraped_content:
            return False
        
        if not api_content:
            return True
        
        # 比较长度（爬取的内容应该更长）
        if len(scraped_content) > len(api_content) * 1.5:
            return True
        
        # 检查是否包含更多有用信息
        scraped_words = set(scraped_content.lower().split())
        api_words = set(api_content.lower().split())
        
        # 如果爬取的内容包含更多独特词汇
        unique_scraped_words = scraped_words - api_words
        if len(unique_scraped_words) > len(api_words) * 0.3:
            return True
        
        return False
