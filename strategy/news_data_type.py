import pyarrow as pa
from nautilus_trader.core.data import Data
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.serialization.base import register_serializable_type
from nautilus_trader.serialization.arrow.serializer import register_arrow
from typing import List, Optional

class NewsData(Data):
    def __init__(
        self,
        instrument_id: InstrumentId,
        title: str,
        content: str,
        url: str,
        publish_time: str,
        source: str = "PANews",
        news_id: str = "",
        category: str = "news",
        confidence: float = 0.0,
        scraped_at: str = "",
        ts_event: int = 0,
        ts_init: int = 0,
        **kwargs
    ):
        self.instrument_id = instrument_id
        self.title = title
        self.content = content
        self.url = url
        self.publish_time = publish_time
        self.source = source
        self.news_id = news_id
        self.category = category
        self.confidence = confidence  # 符号提取的置信度
        self.scraped_at = scraped_at
        self._ts_event = ts_event
        self._ts_init = ts_init

    def __repr__(self) -> str:
        return (
            f"{type(self).__name__}("
            f"instrument_id={self.instrument_id}, "
            f"title='{self.title[:50]}...', "
            f"confidence={self.confidence:.3f}, "
            f"ts_event={self.ts_event}, "
            f"ts_init={self.ts_init})"
        )

    @property
    def ts_event(self) -> int:
        return self._ts_event

    @property
    def ts_init(self) -> int:
        return self._ts_init

    @staticmethod
    def to_dict(obj: "NewsData") -> dict:
        return {
            "instrument_id": obj.instrument_id.value,
            "title": obj.title,
            "content": obj.content,
            "url": obj.url,
            "publish_time": obj.publish_time,
            "source": obj.source,
            "news_id": obj.news_id,
            "category": obj.category,
            "confidence": obj.confidence,
            "scraped_at": obj.scraped_at,
            "ts_event": obj.ts_event,
            "ts_init": obj.ts_init,
        }

    @classmethod
    def from_dict(cls, values: dict) -> "NewsData":
        instrument_id = InstrumentId.from_str(values["instrument_id"])
        return cls(
            instrument_id=instrument_id,
            title=values["title"],
            content=values["content"],
            url=values["url"],
            publish_time=values["publish_time"],
            source=values.get("source", "PANews"),
            news_id=values.get("news_id", ""),
            category=values.get("category", "news"),
            confidence=values.get("confidence", 0.0),
            scraped_at=values.get("scraped_at", ""),
            ts_event=values["ts_event"],
            ts_init=values["ts_init"],
        )

    @classmethod
    def schema(cls):
        fields = [
            pa.field("instrument_id", pa.string(), False),
            pa.field("title", pa.string(), False),
            pa.field("content", pa.string(), False),
            pa.field("url", pa.string(), False),
            pa.field("publish_time", pa.string(), False),
            pa.field("source", pa.string(), False),
            pa.field("news_id", pa.string(), False),
            pa.field("category", pa.string(), False),
            pa.field("confidence", pa.float64(), False),
            pa.field("scraped_at", pa.string(), False),
            pa.field("ts_event", pa.uint64(), False),
            pa.field("ts_init", pa.uint64(), False),
        ]
        return pa.schema(fields)

    @staticmethod
    def to_catalog(obj: "NewsData"):
        return pa.RecordBatch.from_pylist([NewsData.to_dict(obj)], schema=NewsData.schema())

    @classmethod
    def from_catalog(cls, table):
        return [NewsData.from_dict(d) for d in table.to_pylist()]

# 注册序列化
register_serializable_type(NewsData, NewsData.to_dict, NewsData.from_dict)
register_arrow(NewsData, NewsData.schema(), NewsData.to_catalog, NewsData.from_catalog) 