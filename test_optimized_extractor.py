#!/usr/bin/env python3
"""
测试优化后的 NewsInstrumentExtractor
验证从 cache.instruments() 获取数据的功能
"""

from typing import List, Dict
import sys
import os

# 添加当前目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nautilus_trader.model.identifiers import InstrumentId, Venue
from strategy.news_instrument_extractor_v2 import NewsInstrumentExtractor


class MockInstrument:
    """模拟 Instrument 对象"""
    def __init__(self, instrument_id_str: str):
        self.id = MockInstrumentId(instrument_id_str)


class MockInstrumentId:
    """模拟 InstrumentId 对象"""
    def __init__(self, value: str):
        self.value = value


class MockCache:
    """模拟 Cache 对象"""
    def __init__(self, instrument_ids: List[str]):
        self._instruments = [MockInstrument(id_str) for id_str in instrument_ids]
    
    def instruments(self, venue=None):
        """返回模拟的 instruments 列表"""
        return self._instruments


def test_cache_integration():
    """测试从 cache 获取数据的功能"""
    print("=" * 60)
    print("测试从 cache.instruments() 获取数据")
    print("=" * 60)
    
    # 模拟一些 instrument IDs（类似实盘环境，包含特殊前缀）
    mock_instrument_ids = [
        "BTCUSDT-PERP.BINANCE",
        "ETHUSDT-PERP.BINANCE",
        "ADAUSDT-PERP.BINANCE",
        "DOGEUSDT-PERP.BINANCE",
        "SOLUSDT-PERP.BINANCE",
        "LINKUSDT-PERP.BINANCE",
        "DOTUSDT-PERP.BINANCE",
        "UNIUSDT-PERP.BINANCE",
        "AVAXUSDT-PERP.BINANCE",
        "MATICUSDT-PERP.BINANCE",
        "1000PEPEUSDT-PERP.BINANCE",  # PEPE 对应 1000PEPE
        "1000SHIBUSDT-PERP.BINANCE",  # SHIB 对应 1000SHIB
        "1000FLOKIUSDT-PERP.BINANCE", # FLOKI 对应 1000FLOKI
    ]
    
    # 创建模拟 cache
    mock_cache = MockCache(mock_instrument_ids)
    
    # 创建提取器实例
    extractor = NewsInstrumentExtractor(
        cache=mock_cache,
        venue=Venue("BINANCE"),
        max_symbols=3,
        confidence_params={
            'base_multiplier': 0.3,
            'trading_context_bonus': 1.2,
            'max_confidence': 1.0
        },
        threshold_params={
            'min_confidence_with_context': 0.15,
            'min_confidence_without_context': 0.25,
            'min_match_count': 1
        },
        crypto_aliases_path=None  # 不使用别名文件
    )
    
    print(f"从 cache 加载的 binance_symbols 数量: {len(extractor.binance_symbols)}")
    print(f"可用符号数量: {len(extractor.available_symbols)}")
    print(f"符号关键词映射数量: {len(extractor.symbol_keywords)}")
    
    # 显示一些加载的符号
    print("\n加载的符号示例:")
    for i, symbol in enumerate(list(extractor.binance_symbols.keys())[:10]):
        print(f"  {i+1}. {symbol}")
    
    # 测试符号提取
    print("\n" + "=" * 60)
    print("测试符号提取功能")
    print("=" * 60)
    
    test_cases = [
        {
            'title': 'BTC价格突破50000美元，比特币迎来新高',
            'content': '比特币(BTC)今日价格突破50000美元大关，创下历史新高。交易量大幅增加。',
            'expected_symbols': ['BTCUSDT']
        },
        {
            'title': 'Ethereum升级完成，ETH代币价格上涨',
            'content': '以太坊网络升级顺利完成，ETH代币价格应声上涨，交易活跃度显著提升。',
            'expected_symbols': ['ETHUSDT']
        },
        {
            'title': 'DOGE空投活动开始，狗狗币持有者受益',
            'content': '狗狗币(DOGE)空投活动正式开始，持有DOGE的用户可以获得免费代币。',
            'expected_symbols': ['DOGEUSDT']
        },
        {
            'title': 'UNI代币价格上涨，Uniswap交易量激增',
            'content': 'Uniswap协议代币UNI价格大幅上涨，平台交易量创新高。',
            'expected_symbols': ['UNIUSDT']
        },
        {
            'title': 'PEPE meme币热度不减，社区活跃度高',
            'content': 'PEPE代币继续受到关注，社区讨论热烈，交易活跃。',
            'expected_symbols': ['1000PEPEUSDT']
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print(f"标题: {test_case['title']}")
        print(f"内容: {test_case['content'][:50]}...")
        
        # 提取符号
        title_lower = test_case['title'].lower()
        content_lower = test_case['content'].lower()
        
        symbols_with_confidence = extractor.extract_symbols_from_text(title_lower, content_lower)
        extracted_symbols = [symbol for symbol, count, confidence in symbols_with_confidence]
        
        print(f"提取到的符号: {extracted_symbols}")
        print(f"期望的符号: {test_case['expected_symbols']}")
        
        # 检查结果
        if set(extracted_symbols) == set(test_case['expected_symbols']):
            print("✅ 测试通过")
        else:
            print("❌ 测试失败")
        
        # 显示详细信息
        if symbols_with_confidence:
            print("详细信息:")
            for symbol, count, confidence in symbols_with_confidence:
                print(f"  - {symbol}: 匹配次数={count}, 置信度={confidence:.3f}")


def test_instrument_id_extraction():
    """测试 InstrumentId 提取功能"""
    print("\n" + "=" * 60)
    print("测试 InstrumentId 提取功能")
    print("=" * 60)

    # 模拟一些 instrument IDs（包含特殊前缀）
    mock_instrument_ids = [
        "BTCUSDT-PERP.BINANCE",
        "ETHUSDT-PERP.BINANCE",
        "DOGEUSDT-PERP.BINANCE",
        "UNIUSDT-PERP.BINANCE",
        "1000PEPEUSDT-PERP.BINANCE",
        "1000SHIBUSDT-PERP.BINANCE"
    ]

    # 创建模拟 cache
    mock_cache = MockCache(mock_instrument_ids)

    # 创建提取器实例
    extractor = NewsInstrumentExtractor(
        cache=mock_cache,
        venue=Venue("BINANCE"),
        max_symbols=3
    )

    # 显示基础符号映射
    print("基础符号到 InstrumentId 的映射:")
    for base_symbol, instrument_id in extractor.base_symbol_to_instrument_id.items():
        print(f"  {base_symbol} -> {instrument_id}")

    # 测试用例
    test_cases = [
        {
            'title': "BTC价格突破50000美元，比特币迎来新高",
            'content': "比特币(BTC)今日价格突破50000美元大关，创下历史新高。",
            'expected_ids': ["BTCUSDT-PERP.BINANCE"]
        },
        {
            'title': "UNI代币价格上涨，Uniswap交易量激增",
            'content': "Uniswap协议代币UNI价格大幅上涨，平台交易量创新高。",
            'expected_ids': ["UNIUSDT-PERP.BINANCE"]
        },
        {
            'title': "PEPE meme币热度不减，社区活跃度高",
            'content': "PEPE代币继续受到关注，社区讨论热烈，交易活跃。",
            'expected_ids': ["1000PEPEUSDT-PERP.BINANCE"]
        }
    ]

    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print(f"标题: {test_case['title']}")

        instrument_ids = extractor.extract_instrument_ids(
            test_case['title'].lower(),
            test_case['content'].lower()
        )

        extracted_ids = [id.value for id in instrument_ids]
        print(f"提取到的 InstrumentId: {extracted_ids}")
        print(f"期望的 InstrumentId: {test_case['expected_ids']}")

        if set(extracted_ids) == set(test_case['expected_ids']):
            print("✅ 测试通过")
        else:
            print("❌ 测试失败")


if __name__ == "__main__":
    test_cache_integration()
    test_instrument_id_extraction()
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
