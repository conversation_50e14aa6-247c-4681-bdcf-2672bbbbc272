#!/usr/bin/env python3
"""
简化测试：验证优化后的核心功能
"""

import sys
import os
from datetime import datetime

# 添加当前目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nautilus_trader.model.identifiers import Venue, InstrumentId
from strategy.news_instrument_extractor_v2 import NewsInstrumentExtractor
from strategy.news_content_scraper import NewsContentScraper
from strategy.news_data_type import NewsData


class MockInstrument:
    """模拟 Instrument 对象"""
    def __init__(self, instrument_id_str: str):
        self.id = MockInstrumentId(instrument_id_str)


class MockInstrumentId:
    """模拟 InstrumentId 对象"""
    def __init__(self, value: str):
        self.value = value


class MockCache:
    """模拟 Cache 对象"""
    def __init__(self, instrument_ids: list):
        self._instruments = [MockInstrument(id_str) for id_str in instrument_ids]
    
    def instruments(self, venue=None):
        """返回模拟的 instruments 列表"""
        return self._instruments


def test_instrument_extraction():
    """测试交易对提取功能"""
    print("=" * 60)
    print("测试交易对提取功能")
    print("=" * 60)
    
    # 创建模拟 cache，包含一些交易对
    mock_instrument_ids = [
        "BTCUSDT-PERP.BINANCE",
        "ETHUSDT-PERP.BINANCE",
        "DOGEUSDT-PERP.BINANCE",
        "UNIUSDT-PERP.BINANCE",
        "1000PEPEUSDT-PERP.BINANCE",
        "SOLUSDT-PERP.BINANCE",
        "LINKUSDT-PERP.BINANCE",
    ]
    
    mock_cache = MockCache(mock_instrument_ids)
    
    # 创建提取器
    extractor = NewsInstrumentExtractor(
        cache=mock_cache,
        venue=Venue("BINANCE"),
        max_symbols=3,
        confidence_params={
            'base_multiplier': 0.3,
            'trading_context_bonus': 1.2,
            'max_confidence': 1.0
        },
        threshold_params={
            'min_confidence_with_context': 0.1,
            'min_confidence_without_context': 0.2,
            'min_match_count': 1
        }
    )
    
    print(f"✅ 提取器初始化成功")
    print(f"   基础符号映射数量: {len(extractor.base_symbol_to_instrument_id)}")
    print(f"   可用符号数量: {len(extractor.available_symbols)}")
    
    # 测试用例
    test_cases = [
        {
            'title': 'BTC价格突破50000美元，比特币迎来新高',
            'content': '比特币(BTC)今日价格突破50000美元大关，创下历史新高。交易量大幅增加。',
            'expected': ['BTCUSDT-PERP.BINANCE']
        },
        {
            'title': 'UNI代币价格上涨，Uniswap交易量激增',
            'content': 'Uniswap协议代币UNI价格大幅上涨，平台交易量创新高。',
            'expected': ['UNIUSDT-PERP.BINANCE']
        },
        {
            'title': 'PEPE meme币热度不减，社区活跃度高',
            'content': 'PEPE代币继续受到关注，社区讨论热烈，交易活跃。',
            'expected': ['1000PEPEUSDT-PERP.BINANCE']
        },
        {
            'title': 'LINK预言机网络升级完成',
            'content': 'Chainlink网络升级顺利完成，LINK代币价格应声上涨。',
            'expected': ['LINKUSDT-PERP.BINANCE']
        }
    ]
    
    print("\n测试交易对提取:")
    success_count = 0
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. {case['title'][:50]}...")
        
        # 提取交易对
        instrument_ids = extractor.extract_instrument_ids(
            case['title'].lower(),
            case['content'].lower()
        )
        
        extracted_ids = [id.value for id in instrument_ids]
        expected_ids = case['expected']
        
        print(f"   提取结果: {extracted_ids}")
        print(f"   期望结果: {expected_ids}")
        
        if set(extracted_ids) == set(expected_ids):
            print("   ✅ 测试通过")
            success_count += 1
        else:
            print("   ❌ 测试失败")
    
    print(f"\n总结: {success_count}/{len(test_cases)} 个测试通过")
    return success_count == len(test_cases)


def test_content_scraping():
    """测试内容爬取功能"""
    print("\n" + "=" * 60)
    print("测试内容爬取功能")
    print("=" * 60)
    
    # 创建爬取器
    scraper = NewsContentScraper(timeout=10, use_proxy=False)
    
    # 测试一个简单的URL
    test_url = "https://httpbin.org/html"
    
    try:
        print(f"测试URL: {test_url}")
        result = scraper.scrape_content(test_url)
        
        if result:
            print("✅ 内容爬取成功")
            print(f"   标题: {result.get('title', 'N/A')}")
            print(f"   内容长度: {len(result.get('content', ''))}")
            print(f"   发布时间: {result.get('publish_time', 'N/A')}")
            return True
        else:
            print("❌ 内容爬取返回空结果")
            return False
            
    except Exception as e:
        print(f"❌ 内容爬取失败: {e}")
        return False


def test_news_data_creation():
    """测试NewsData创建"""
    print("\n" + "=" * 60)
    print("测试NewsData创建")
    print("=" * 60)
    
    try:
        # 创建测试数据
        instrument_id = InstrumentId.from_str("BTCUSDT-PERP.BINANCE")
        
        news_data = NewsData(
            instrument_id=instrument_id,
            title="测试新闻标题",
            content="这是一条测试新闻内容，用于验证NewsData对象的创建。",
            url="https://example.com/news/123",
            publish_time="2024-01-01 12:00:00",
            source="PANews",
            news_id="test_123",
            category="news",
            confidence=0.85,
            scraped_at=datetime.now().isoformat(),
            ts_event=1640995200000000000,  # 示例时间戳
            ts_init=1640995200000000000
        )
        
        print("✅ NewsData创建成功")
        print(f"   交易对: {news_data.instrument_id.value}")
        print(f"   标题: {news_data.title}")
        print(f"   置信度: {news_data.confidence}")
        print(f"   来源: {news_data.source}")
        
        # 测试序列化
        data_dict = NewsData.to_dict(news_data)
        print(f"   序列化成功: {len(data_dict)} 个字段")
        
        # 测试反序列化
        restored_data = NewsData.from_dict(data_dict)
        print(f"   反序列化成功: {restored_data.instrument_id.value}")
        
        return True
        
    except Exception as e:
        print(f"❌ NewsData创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_integration():
    """集成测试：模拟完整的新闻处理流程"""
    print("\n" + "=" * 60)
    print("集成测试：完整新闻处理流程")
    print("=" * 60)
    
    try:
        # 1. 模拟新闻数据
        mock_news = {
            'title': 'BTC价格突破50000美元，比特币迎来新高',
            'content': '比特币(BTC)今日价格突破50000美元大关，创下历史新高。这一突破标志着加密货币市场的重要里程碑。',
            'url': 'https://example.com/news/btc-50k',
            'publish_time': '2024-01-01 12:00:00'
        }
        
        print(f"1. 模拟新闻: {mock_news['title'][:50]}...")
        
        # 2. 提取交易对
        mock_instrument_ids = ["BTCUSDT-PERP.BINANCE", "ETHUSDT-PERP.BINANCE"]
        mock_cache = MockCache(mock_instrument_ids)
        
        extractor = NewsInstrumentExtractor(
            cache=mock_cache,
            venue=Venue("BINANCE"),
            max_symbols=3
        )
        
        instrument_ids = extractor.extract_instrument_ids(
            mock_news['title'].lower(),
            mock_news['content'].lower()
        )
        
        print(f"2. 提取到交易对: {[id.value for id in instrument_ids]}")
        
        if not instrument_ids:
            print("❌ 未提取到交易对，测试失败")
            return False
        
        # 3. 创建NewsData对象
        news_data_list = []
        for instrument_id in instrument_ids:
            # 计算置信度
            symbols_with_confidence = extractor.extract_symbols_from_text(
                mock_news['title'].lower(),
                mock_news['content'].lower()
            )
            
            confidence = 0.5  # 默认置信度
            for symbol, count, conf in symbols_with_confidence:
                if symbol.upper() in instrument_id.value:
                    confidence = conf
                    break
            
            news_data = NewsData(
                instrument_id=instrument_id,
                title=mock_news['title'],
                content=mock_news['content'],
                url=mock_news['url'],
                publish_time=mock_news['publish_time'],
                source="PANews",
                news_id="test_integration_123",
                category="news",
                confidence=confidence,
                scraped_at=datetime.now().isoformat(),
                ts_event=1640995200000000000,
                ts_init=1640995200000000000
            )
            
            news_data_list.append(news_data)
        
        print(f"3. 创建了 {len(news_data_list)} 个NewsData对象")
        
        # 4. 显示结果
        for i, news_data in enumerate(news_data_list, 1):
            print(f"   {i}. {news_data.instrument_id.value} (置信度: {news_data.confidence:.3f})")
        
        print("✅ 集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("开始优化功能测试")
    print("=" * 60)
    
    results = []
    
    # 运行各项测试
    results.append(("交易对提取", test_instrument_extraction()))
    results.append(("内容爬取", test_content_scraping()))
    results.append(("NewsData创建", test_news_data_creation()))
    results.append(("集成测试", test_integration()))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试都通过了！优化功能正常工作。")
    else:
        print("⚠️ 部分测试失败，需要进一步调试。")


if __name__ == "__main__":
    main()
