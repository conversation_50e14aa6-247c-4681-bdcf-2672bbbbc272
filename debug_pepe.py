#!/usr/bin/env python3
"""
调试 PEPE 符号识别问题
"""

from typing import List, Dict
import sys
import os

# 添加当前目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nautilus_trader.model.identifiers import InstrumentId, Venue
from strategy.news_instrument_extractor_v2 import NewsInstrumentExtractor


class MockInstrument:
    """模拟 Instrument 对象"""
    def __init__(self, instrument_id_str: str):
        self.id = MockInstrumentId(instrument_id_str)


class MockInstrumentId:
    """模拟 InstrumentId 对象"""
    def __init__(self, value: str):
        self.value = value


class MockCache:
    """模拟 Cache 对象"""
    def __init__(self, instrument_ids: List[str]):
        self._instruments = [MockInstrument(id_str) for id_str in instrument_ids]
    
    def instruments(self, venue=None):
        """返回模拟的 instruments 列表"""
        return self._instruments


def debug_pepe_recognition():
    """调试 PEPE 符号识别"""
    print("=" * 60)
    print("调试 PEPE 符号识别问题")
    print("=" * 60)
    
    # 模拟包含 PEPE 的 instrument IDs
    mock_instrument_ids = [
        "BTCUSDT-PERP.BINANCE",
        "1000PEPEUSDT-PERP.BINANCE",
    ]
    
    # 创建模拟 cache
    mock_cache = MockCache(mock_instrument_ids)
    
    # 创建提取器实例
    extractor = NewsInstrumentExtractor(
        cache=mock_cache,
        venue=Venue("BINANCE"),
        max_symbols=3
    )
    
    print("1. 检查 binance_symbols:")
    for symbol in extractor.binance_symbols.keys():
        print(f"  - {symbol}")
    
    print("\n2. 检查 symbol_keywords 映射:")
    for keyword, symbol in extractor.symbol_keywords.items():
        print(f"  {keyword} -> {symbol}")
    
    print("\n3. 检查 base_symbol_to_instrument_id 映射:")
    for base_symbol, instrument_id in extractor.base_symbol_to_instrument_id.items():
        print(f"  {base_symbol} -> {instrument_id}")
    
    print("\n4. 测试 PEPE 文本提取:")
    title = "PEPE meme币热度不减，社区活跃度高"
    content = "PEPE代币继续受到关注，社区讨论热烈，交易活跃。"
    
    print(f"标题: {title}")
    print(f"内容: {content}")
    
    # 测试标题符号提取
    title_symbols = extractor.extract_symbols_from_original_title(title)
    print(f"\n从标题提取的符号: {title_symbols}")
    
    # 测试完整的符号提取
    symbols_with_confidence = extractor.extract_symbols_from_text(title.lower(), content.lower())
    print(f"完整提取结果: {symbols_with_confidence}")
    
    # 测试 PEPE 是否在黑名单中
    print(f"\nPEPE 是否在黑名单中: {'pepe' in extractor.blacklist}")
    
    # 手动测试 PEPE 匹配
    print("\n5. 手动测试 PEPE 匹配:")
    test_text = "pepe代币继续受到关注"
    for keyword in extractor.symbol_keywords.keys():
        if 'pepe' in keyword:
            print(f"关键词 '{keyword}' 在文本中: {keyword in test_text}")


if __name__ == "__main__":
    debug_pepe_recognition()
